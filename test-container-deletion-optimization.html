<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>容器删除优化测试 - TechManWorkflow JointJS</title>
    <link rel="stylesheet" href="js/lib/jointjs-3.7.7.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            overflow: hidden;
        }

        #sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 250px;
            height: 100vh;
            background: white;
            border-right: 1px solid #ddd;
            padding: 20px;
            box-sizing: border-box;
            z-index: 1000;
            overflow-y: auto;
        }

        #paper-container {
            position: absolute;
            left: 250px;
            top: 0;
            right: 0;
            bottom: 0;
            background: #f9f9f9;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .test-section h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 16px;
        }

        .test-button {
            display: block;
            width: 100%;
            padding: 10px;
            margin: 8px 0;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        .test-button:hover {
            background: #0056b3;
        }

        .test-button.danger {
            background: #dc3545;
        }

        .test-button.danger:hover {
            background: #c82333;
        }

        .test-button.success {
            background: #28a745;
        }

        .test-button.success:hover {
            background: #218838;
        }

        .info-panel {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 12px;
            margin: 10px 0;
            font-size: 13px;
            line-height: 1.4;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.green {
            background: #28a745;
        }

        .status-indicator.red {
            background: #dc3545;
        }

        .status-indicator.yellow {
            background: #ffc107;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div id="sidebar">
        <h2 style="margin-top: 0; color: #333;">容器删除优化测试</h2>
        
        <div class="info-panel">
            <strong>测试目标：</strong><br>
            验证容器删除时，嵌套节点保持位置和连接，只删除容器本身。
        </div>

        <div class="test-section">
            <h3>1. 创建测试场景</h3>
            <button class="test-button" onclick="createTestScenario()">创建测试容器和节点</button>
            <button class="test-button" onclick="createConnections()">创建节点连接</button>
        </div>

        <div class="test-section">
            <h3>2. 验证删除行为</h3>
            <button class="test-button danger" onclick="deleteContainer()">删除容器节点</button>
            <button class="test-button success" onclick="verifyResults()">验证结果</button>
        </div>

        <div class="test-section">
            <h3>3. 测试状态</h3>
            <div id="test-status">
                <div><span class="status-indicator yellow"></span>等待开始测试</div>
            </div>
        </div>

        <div class="test-section">
            <h3>4. 重置测试</h3>
            <button class="test-button" onclick="resetTest()">清空画布</button>
        </div>

        <div class="info-panel">
            <strong>预期行为：</strong><br>
            • 容器删除后，嵌套节点保持原位置<br>
            • 节点间的连接线保持完整<br>
            • 嵌套节点可以正常选择和移动<br>
            • 容器相关UI元素被清理
        </div>
    </div>

    <!-- 主画布容器 -->
    <div id="paper-container"></div>

    <!-- JointJS依赖库 -->
    <script src="js/lib/jquery-3.7.1.min.js"></script>
    <script src="js/lib/underscore-1.13.6.min.js"></script>
    <script src="js/lib/backbone-1.4.1.min.js"></script>
    <script src="js/lib/jointjs-3.7.7.min.js"></script>

    <!-- 应用模块 -->
    <script src="js/core/constants.js"></script>
    <script src="js/utils/helpers.js"></script>
    <script src="js/core/graph.js"></script>
    <script src="js/features/node-manager.js"></script>

    <script>
        // 测试应用实例
        let app = null;
        let nodeManager = null;
        let testContainer = null;
        let testNodes = [];
        let testLinks = [];

        // 初始化测试应用
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                console.log('初始化容器删除优化测试...');
                
                // 创建工作流应用实例
                app = new WorkflowApp();
                await app.init();
                
                // 创建节点管理器
                nodeManager = new NodeManager(app);
                
                updateStatus('应用初始化完成', 'green');
                console.log('测试应用初始化完成');
                
            } catch (error) {
                console.error('测试应用初始化失败:', error);
                updateStatus('初始化失败: ' + error.message, 'red');
            }
        });

        // 创建测试场景
        function createTestScenario() {
            try {
                console.log('创建测试场景...');
                
                // 清空现有内容
                app.graph.clear();
                testNodes = [];
                testLinks = [];
                testContainer = null;
                
                // 创建容器节点
                testContainer = nodeManager.createContainerNode(400, 300);
                testContainer.resize(300, 200);
                
                // 在容器内创建几个节点
                const node1 = nodeManager.createProcessNode(350, 250);
                const node2 = nodeManager.createDecisionNode(450, 250);
                const node3 = nodeManager.createProcessNode(350, 350);
                
                testNodes = [node1, node2, node3];
                
                // 手动嵌套节点到容器中
                setTimeout(() => {
                    testNodes.forEach(node => {
                        testContainer.embed(node);
                        node.toFront();
                    });
                    
                    updateStatus('测试场景创建完成 - 容器包含3个节点', 'green');
                    console.log('测试场景创建完成');
                }, 100);
                
            } catch (error) {
                console.error('创建测试场景失败:', error);
                updateStatus('创建场景失败: ' + error.message, 'red');
            }
        }

        // 创建节点连接
        function createConnections() {
            try {
                if (testNodes.length < 3) {
                    updateStatus('请先创建测试场景', 'red');
                    return;
                }
                
                console.log('创建节点连接...');
                
                // 创建连接线
                const link1 = new joint.shapes.standard.Link();
                link1.source(testNodes[0]);
                link1.target(testNodes[1]);
                link1.addTo(app.graph);
                
                const link2 = new joint.shapes.standard.Link();
                link2.source(testNodes[1]);
                link2.target(testNodes[2]);
                link2.addTo(app.graph);
                
                testLinks = [link1, link2];
                
                updateStatus('节点连接创建完成', 'green');
                console.log('节点连接创建完成');
                
            } catch (error) {
                console.error('创建连接失败:', error);
                updateStatus('创建连接失败: ' + error.message, 'red');
            }
        }

        // 删除容器
        function deleteContainer() {
            try {
                if (!testContainer) {
                    updateStatus('请先创建测试场景', 'red');
                    return;
                }
                
                console.log('删除容器节点...');
                
                // 记录删除前的节点位置和连接
                const nodePositionsBefore = testNodes.map(node => ({
                    id: node.id,
                    position: node.position(),
                    exists: !!app.graph.getCell(node.id)
                }));
                
                const linksBefore = testLinks.map(link => ({
                    id: link.id,
                    exists: !!app.graph.getCell(link.id)
                }));
                
                console.log('删除前状态:', { nodePositionsBefore, linksBefore });
                
                // 使用优化的删除方法
                nodeManager.deleteNode(testContainer);
                
                // 验证删除结果
                setTimeout(() => {
                    const nodePositionsAfter = testNodes.map(node => ({
                        id: node.id,
                        position: node.position(),
                        exists: !!app.graph.getCell(node.id)
                    }));
                    
                    const linksAfter = testLinks.map(link => ({
                        id: link.id,
                        exists: !!app.graph.getCell(link.id)
                    }));
                    
                    console.log('删除后状态:', { nodePositionsAfter, linksAfter });
                    
                    updateStatus('容器已删除，请验证结果', 'yellow');
                }, 200);
                
            } catch (error) {
                console.error('删除容器失败:', error);
                updateStatus('删除容器失败: ' + error.message, 'red');
            }
        }

        // 验证结果
        function verifyResults() {
            try {
                console.log('验证测试结果...');
                
                let results = [];
                let allPassed = true;
                
                // 检查容器是否被删除
                const containerExists = !!app.graph.getCell(testContainer.id);
                if (containerExists) {
                    results.push('❌ 容器节点未被删除');
                    allPassed = false;
                } else {
                    results.push('✅ 容器节点已正确删除');
                }
                
                // 检查嵌套节点是否保留
                let nodesPreserved = 0;
                testNodes.forEach(node => {
                    if (app.graph.getCell(node.id)) {
                        nodesPreserved++;
                    }
                });
                
                if (nodesPreserved === testNodes.length) {
                    results.push('✅ 所有嵌套节点已保留');
                } else {
                    results.push(`❌ 只有${nodesPreserved}/${testNodes.length}个节点被保留`);
                    allPassed = false;
                }
                
                // 检查连接线是否保留
                let linksPreserved = 0;
                testLinks.forEach(link => {
                    if (app.graph.getCell(link.id)) {
                        linksPreserved++;
                    }
                });
                
                if (linksPreserved === testLinks.length) {
                    results.push('✅ 所有连接线已保留');
                } else {
                    results.push(`❌ 只有${linksPreserved}/${testLinks.length}个连接线被保留`);
                    allPassed = false;
                }
                
                // 检查节点是否可以交互
                const firstNode = testNodes[0];
                if (firstNode && app.graph.getCell(firstNode.id)) {
                    const elementView = app.paper.findViewByModel(firstNode);
                    if (elementView) {
                        results.push('✅ 节点可以正常交互');
                    } else {
                        results.push('❌ 节点无法正常交互');
                        allPassed = false;
                    }
                }
                
                const statusText = allPassed ? 
                    '测试通过！所有功能正常工作' : 
                    '测试失败！存在问题需要修复';
                
                const statusColor = allPassed ? 'green' : 'red';
                
                updateStatus(statusText, statusColor);
                
                console.log('验证结果:', results);
                alert(results.join('\n'));
                
            } catch (error) {
                console.error('验证结果失败:', error);
                updateStatus('验证失败: ' + error.message, 'red');
            }
        }

        // 重置测试
        function resetTest() {
            try {
                app.graph.clear();
                testNodes = [];
                testLinks = [];
                testContainer = null;
                updateStatus('测试已重置', 'yellow');
                console.log('测试已重置');
            } catch (error) {
                console.error('重置测试失败:', error);
                updateStatus('重置失败: ' + error.message, 'red');
            }
        }

        // 更新状态显示
        function updateStatus(message, color = 'yellow') {
            const statusElement = document.getElementById('test-status');
            statusElement.innerHTML = `<div><span class="status-indicator ${color}"></span>${message}</div>`;
        }
    </script>
</body>
</html>
